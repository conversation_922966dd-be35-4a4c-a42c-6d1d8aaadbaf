import React from 'react';
import { Row, Col, Button, Radio, Empty, message } from 'antd';
import { connect, type Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import styles from './FusionModalContent.less';

interface SeriesSelectModalProps {
    dispatch: Dispatch;
    onOk?: () => void;
    onCancel?: () => void;
}

type Props = SeriesSelectModalProps & {
    seriesLists?: any[];
    selectedSeriesId?: string;
};

const SeriesSelectModal: React.FC<Props> = React.memo((props) => {
    const { dispatch, seriesLists, selectedSeriesId, onOk, onCancel } = props;

    // 处理确定按钮点击
    const handleOk = () => {
        if (selectedSeriesId) {
            // 确定后才切换到选中的序列
            dispatch({
                type: 'views/save',
                payload: {
                    curSeriesId: selectedSeriesId,
                },
            });

            // 获取选中序列的instances
            dispatch({
                type: 'views/fetchInstances',
                payload: selectedSeriesId,
            });

            message.success('序列选择成功');
            onOk?.();
        }
    };

    // 处理序列选择 - 只更新选中的序列ID，不切换当前显示的序列
    const handleSeriesSelect = (seriesId: string) => {
        dispatch({
            type: 'views/save',
            payload: {
                selectedSeriesId: seriesId,
                // 不更新 currentSeriesId，保持当前显示的序列不变
            },
        });
    };

    // 渲染序列表格
    const renderSeriesTable = () => {
        if (!seriesLists || seriesLists.length === 0) {
            return <Empty description='暂无数据' image={Empty.PRESENTED_IMAGE_SIMPLE} />;
        }

        return (
            <div>
                <div className={styles.tableHeader}>
                    <div className={styles.radioCell}></div>
                    <div className={styles.sequenceCell}>序号</div>
                    <div className={styles.descriptionCell}>序列描述</div>
                </div>
                <div>
                    {seriesLists.map((series, index) => {
                        const isSelected = selectedSeriesId === series.SeriesId;

                        return (
                            <div key={series.SeriesId} className={`${styles.tableRow} ${isSelected ? styles.selected : ''}`}>
                                <div className={styles.radioCell}>
                                    <Radio
                                        checked={isSelected}
                                        onChange={() => {
                                            handleSeriesSelect(series.SeriesId);
                                        }}
                                    />
                                </div>
                                <div className={`${styles.sequenceCell} ${isSelected ? styles.selected : ''}`}>
                                    {isSelected ? <span style={{ color: '#1890ff' }}>{index + 1}</span> : index + 1}
                                </div>
                                <div className={`${styles.descriptionCell} ${isSelected ? styles.selected : ''}`}>
                                    {series.SeriesDescription || '无描述'}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    return (
        <div className={styles.fusionModalContent}>
            <Row gutter={16} style={{ margin: 0 }}>
                <Col span={24} style={{ paddingLeft: 0, paddingRight: 0 }}>
                    <div className={styles.seriesListContainer}>{renderSeriesTable()}</div>
                </Col>
            </Row>
            <div className={styles.modalFooter}>
                <Button onClick={onCancel}>取消</Button>
                <Button
                    type={!selectedSeriesId ? undefined : 'primary'}
                    disabled={!selectedSeriesId}
                    onClick={handleOk}
                    style={
                        !selectedSeriesId
                            ? {
                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                borderColor: 'rgb(126, 126, 127)',
                                color: 'rgb(126, 126, 127)',
                                textShadow: 'none',
                                boxShadow: 'none',
                                cursor: 'not-allowed',
                            }
                            : undefined
                    }
                >
                    确定
                </Button>
            </div>
        </div>
    );
});

const mapStateToProps = ({ views }: { views: ViewType }) => {
    return {
        seriesLists: views.seriesLists,
        selectedSeriesId: views.selectedSeriesId,
    };
};

export default connect(mapStateToProps)(SeriesSelectModal); 