import React, { useEffect, useRef, useState } from 'react';
import { type Dispatch } from 'umi';
import { connect } from 'dva';
import { Row, Col, Empty, Button, Spin } from 'antd';
import withAuth from '@/hocs/withAuth';
import { ViewType } from '@/models/views';
import storeUtil from '@/utils/store';

import LeftToolBar from './components/LeftToolBar';
import DisplayArea from './components/DisplayArea';
import DicomArea from './components/DicomArea';
import DicomFusion from './components/DicomFusion';
import RightInformation from './components/RightInformation';

interface InfoProps {
  dispatch: Dispatch;
  pid?: number;
  studyLists?: any[];
  seriesLists?: any[];
  instanceLists?: any[];
  views: ViewType;
}

type Props = InfoProps & {
  isFusionMode?: boolean;
};

const Viewer: React.FC<Props> = ({ dispatch, studyLists, seriesLists, instanceLists, isFusionMode, views }) => {
  // 添加加载状态
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 从URL参数中获取studyId和seriesId
    const urlParams = new URLSearchParams(window.location.search);
    let studyId = urlParams.get('studyId');
    let seriesId = urlParams.get('seriesId');

    // 如果URL中没有studyId，才尝试从localStorage获取
    // 这样可以确保每次从URL进入时都使用URL中的参数
    if (!studyId) {
      studyId = storeUtil.get('studyId')?.value;
    } else {
      // 如果URL中有studyId，则更新localStorage
      storeUtil.set('studyId', studyId);
    }

    // 确保 curInstances 是一个有效的 Map 对象
    if (!views.curInstances || typeof views.curInstances.get !== 'function') {
      dispatch({
        type: 'views/save',
        payload: { curInstances: new Map() },
      });
    }

    // 如果获取到了studyId，先清除旧数据，然后保存新的studyId并加载数据
    if (studyId) {
      setLoading(true);

      // 清除旧数据
      dispatch({
        type: 'views/save',
        payload: {
          seriesLists: [],
          instanceLists: [],
          curInstances: new Map(),
          displayedSeriesList: [],
        },
      });

      // 如果有指定的seriesId，先保存到model中
      if (seriesId) {
        dispatch({
          type: 'views/save',
          payload: {
            selectedSeriesId: seriesId,
            curSeriesId: seriesId,
          },
        });
      }

      // 保存新的studyId
      dispatch({
        type: 'views/save',
        payload: { studyId },
      });

      // 触发获取series的请求
      dispatch({
        type: 'views/fetchSeries',
        payload: { StudyId: studyId },
      });

      // 数据加载完成
      setLoading(false);
    }
  }, []);

  // 当seriesLists加载完成后，设置loading为false，并处理seriesId参数
  useEffect(() => {
    if (seriesLists && seriesLists.length > 0) {
      setLoading(false);

      // 从URL参数中获取seriesId
      const urlParams = new URLSearchParams(window.location.search);
      const seriesId = urlParams.get('seriesId');

      // 如果URL中有seriesId，则设置为选中的序列
      if (seriesId) {
        const seriesExists = seriesLists.some((series) => series.SeriesId === seriesId);

        if (seriesExists) {
          dispatch({
            type: 'views/save',
            payload: {
              selectedSeriesId: seriesId,
              curSeriesId: seriesId,
            },
          });

          // 获取选中序列的instances
          dispatch({
            type: 'views/fetchInstances',
            payload: seriesId,
          });
        } else {
          selectDefaultSeries();
        }
      } else {
        // 如果URL中没有seriesId，则默认选择第一个序列

        selectDefaultSeries();
      }
    } else {
    }
  }, [seriesLists]);

  // 选择默认序列（第一个）
  const selectDefaultSeries = () => {
    if (seriesLists && seriesLists.length > 0) {
      const defaultSeriesId = seriesLists[0].SeriesId;

      dispatch({
        type: 'views/save',
        payload: {
          selectedSeriesId: defaultSeriesId,
          curSeriesId: defaultSeriesId,
        },
      });

      // 获取选中序列的instances
      dispatch({
        type: 'views/fetchInstances',
        payload: defaultSeriesId,
      });
    }
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      {loading ? (
        <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin size='large' tip='加载中...' />
        </div>
      ) : (
        <Row style={{ height: '100%', margin: 0 }}>
          <Col flex="110px" style={{ height: '100%', padding: 0 }}>
            <LeftToolBar></LeftToolBar>
          </Col>
          <Col flex="auto" style={{ height: '100%', padding: 0 }}>
            {/* <DisplayArea></DisplayArea> */}
            {isFusionMode ? <DicomFusion /> : <DicomArea />}
          </Col>
          <Col flex="305px" style={{ height: '100%', padding: 0 }}>
            <RightInformation></RightInformation>
          </Col>
        </Row>
      )}
    </div>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    isFusionMode: views.isFusionMode,
    views,
  };
};

export default connect(mapStateToProps)(Viewer);
